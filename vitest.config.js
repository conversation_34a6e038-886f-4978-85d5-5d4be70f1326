import { defineWorkersConfig, readD1Migrations } from '@cloudflare/vitest-pool-workers/config';
import path from 'node:path';

export default defineWorkersConfig(async () => {
  const migrationsPath = path.join(__dirname, 'migrations');
  const migrations = await readD1Migrations(migrationsPath);

  return {
    test: {
      testTimeout: 15000, // 15 seconds for tests
      hookTimeout: 20000, // 20 seconds for hooks
      setupFiles: ['./test/setup.js'],
      poolOptions: {
        workers: {
          wrangler: { configPath: './wrangler.jsonc', environment: 'dev' },
          miniflare: {
            bindings: {
              TEST_MIGRATIONS: migrations,
            },
          },
        },
      },
    },
  };
});
