-- Migration number: 0002 	 2025-07-14T09:43:44.943Z
-- creates a temp table, copies the data in, then recreates the original table with valid JSON check

-------------------------  meta  ------------------------------
CREATE TABLE meta_temp AS
SELECT * FROM meta;

DROP TABLE IF EXISTS meta;

CREATE TABLE IF NOT EXISTS meta (
  `key` TEXT PRIMARY KEY,
  `value` TEXT NOT NULL CHECK (json_valid(value)),
  `created_at` TEXT NOT NULL DEFAULT (datetime('now')),
  `updated_at` TEXT NOT NULL DEFAULT (datetime('now'))
);

INSERT INTO meta
SELECT * FROM meta_temp;

DROP TABLE meta_temp;

---------------------- applications -----------------------------
CREATE TABLE applications_temp AS
SELECT * FROM applications;

DROP TABLE IF EXISTS applications;

CREATE TABLE IF NOT EXISTS applications (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  uuid TEXT UNIQUE NOT NULL,
  `version` INTEGER NOT NULL,
  `status` TEXT NOT NULL,
  domain TEXT NOT NULL,
  preQualifyFields TEXT NOT NULL CHECK (json_valid(preQualifyFields)), -- JSON
  approvalAmount INTEGER DEFAULT 0 CHECK (approvalAmount >= 0),
  agent TEXT CHECK (json_valid(agent)), -- JSON
  reason TEXT, -- denial reason if denied
  applicationFields TEXT CHECK (json_valid(applicationFields)), -- JSON
  pandadoc TEXT CHECK (json_valid(pandadoc)), -- JSON
  utm TEXT CHECK (json_valid(utm)), -- JSON
  meta TEXT CHECK (json_valid(meta)), -- JSON
  fastTrack INTEGER NOT NULL DEFAULT 0 CHECK (fastTrack IN (0, 1)), -- BOOLEAN as INTEGER (0/1)
  salesforce_id VARCHAR(18) UNIQUE CHECK (length(salesforce_id) = 18), -- Salesforce record ID of Deal__c (18 chars)
  bank_stmts INTEGER CHECK (bank_stmts >= 0), -- how many bank statements were uploaded, 0 means it was skipped
  created_at TEXT NOT NULL,
  updated_at TEXT NOT NULL,
  started_at TEXT,
  submitted_at TEXT,
  signed_at TEXT,
  completed_at TEXT,
  edited_at TEXT
);

INSERT INTO applications
SELECT * FROM applications_temp;

DROP TABLE applications_temp;