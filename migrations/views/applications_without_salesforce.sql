DROP VIEW IF EXISTS applications_without_salesforce;

CREATE VIEW applications_without_salesforce AS
SELECT
  id,
  uuid,
  version,
  status,
  domain,
  fastTrack,
  json_extract(agent, '$.name') AS agent_name,
  json_extract(agent, '$.id') AS agent_id,

  json_extract(preQualifyFields, '$.fundingAmount')      AS fundingAmount,
  json_extract(preQualifyFields, '$.purpose')            AS purpose,
  json_extract(preQualifyFields, '$.topPriority')        AS topPriority,
  json_extract(preQualifyFields, '$.timeline')           AS timeline,
  json_extract(preQualifyFields, '$.businessName')       AS businessName,
  json_extract(preQualifyFields, '$.monthlyRevenue')     AS monthlyRevenue,
  json_extract(preQualifyFields, '$.businessStartDate')  AS businessStartDate,
  json_extract(preQualifyFields, '$.firstName')          AS firstName,
  json_extract(preQualifyFields, '$.lastName')           AS lastName,
  json_extract(preQualifyFields, '$.email')              AS email,
  json_extract(preQualifyFields, '$.phone')              AS phone,
  json_extract(preQualifyFields, '$.estimatedFICO')      AS estimatedFICO,

  salesforce_id,
  bank_stmts,

  json_extract(utm, '$.utm_rep')      AS utm_rep,
  json_extract(utm, '$.utm_source')   AS utm_source,
  json_extract(utm, '$.utm_campaign') AS utm_campaign,
  json_extract(utm, '$.utm_site')     AS utm_site,
  json_extract(utm, '$.utm_af')       AS utm_af,
  json_extract(utm, '$.utm_dur')      AS utm_dur,

  created_at
FROM applications
WHERE salesforce_id IS NULL;
