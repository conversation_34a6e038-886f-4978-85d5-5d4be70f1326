<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <style>
      body {
        margin: 0;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
        background: #f6f9fc;
        color: #23272f;
        padding: 0;
      }
      .container {
        max-width: 540px;
        background: #fff;
        margin: 48px auto 0 auto;
        padding: 0;
        border-radius: 20px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        overflow: hidden;
      }
      .header {
        background: #2563eb;
        color: #fff;
        padding: 2.2rem 2.2rem 1.2rem 2.2rem;
        text-align: left;
      }
      .header h1 {
        font-size: 2rem;
        font-weight: 700;
        margin: 0 0 0.5rem 0;
        letter-spacing: -1px;
      }
      .sf-badge {
        display: inline-block;
        background: #fff;
        color: #2563eb;
        font-weight: 600;
        padding: 0.35em 1.1em;
        border-radius: 999px;
        font-size: 0.98rem;
        margin-top: 0.7rem;
        margin-bottom: 0.2rem;
        text-decoration: none;
        box-shadow: 0 2px 8px rgba(56, 189, 248, 0.1);
        transition: background 0.18s, color 0.18s;
      }
      .sf-badge:hover {
        background: #0341c9;
        color: #fff;
      }
      .content {
        padding: 2.2rem 2.2rem 1.2rem 2.2rem;
      }
      .section {
        font-size: 1rem;
        margin-bottom: 2.2rem;
      }
      .section-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: #2563eb;
        margin-bottom: 1.1rem;
        letter-spacing: 0.01em;
      }
      .field {
        margin-bottom: 1.1rem;
        display: flex;
        flex-wrap: wrap;
        align-items: center;
      }
      .label {
        font-weight: bold;
        color: #6b7280;
        margin-right: 0.5rem;
        min-width: 130px;
      }
      .value {
        color: #23272f;
        font-weight: 400;
        word-break: break-word;
      }
      .footer {
        background: #f6f9fc;
        padding: 1.2rem 2.2rem;
        font-size: 13px;
        color: #a0aec0;
        text-align: center;
        border-radius: 0 0 20px 20px;
      }
      @media (max-width: 650px) {
        .container {
          padding: 0;
          border-radius: 0;
        }
        .header,
        .content,
        .footer {
          padding: 1.1rem 4vw;
        }
        .header h1 {
          font-size: 1.3rem;
        }
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <h1>New FastTrack Submission</h1>
        {{#salesforceDeal}}
        <a class="sf-badge" href="{{url}}" target="_blank" rel="noopener noreferrer">Salesforce: {{name}}</a>
        {{/salesforceDeal}}
      </div>
      <div class="content">
        {{#preQualifyFields}}
        <div class="section">
          <div class="section-title">Personal Info</div>
          <div class="field">
            <span class="label">Business Name:</span>
            <span class="value">{{businessName}}</span>
          </div>
          <div class="field">
            <span class="label">Name:</span>
            <span class="value">{{firstName}} {{lastName}}</span>
          </div>
          <div class="field">
            <span class="label">Email:</span>
            <span class="value">{{email}}</span>
          </div>
          <div class="field">
            <span class="label">Phone:</span>
            <span class="value">{{phone}}</span>
          </div>
          <div class="field">
            <span class="label">Estimated FICO:</span>
            <span class="value">{{estimatedFICO}}</span>
          </div>
        </div>
        {{/preQualifyFields}} {{#agent}}
        <div class="section">
          <div class="section-title">Assigned Agent</div>
          <div class="field">
            <span class="label">Name:</span>
            <span class="value">{{name}}</span>
          </div>
          <div class="field">
            <span class="label">Email:</span>
            <span class="value">{{email}}</span>
          </div>
        </div>
        {{/agent}}
        <div class="section">
          <div class="section-title">Resume URL</div>
          <span>{{resumeUrl}}</span>
        </div>
        {{#utm}}
        <div class="section">
          <div class="section-title">UTM</div>
          {{#utm_source}}
          <div class="field">
            <span class="label">UTM Source:</span>
            <span class="value">{{.}}</span>
          </div>
          {{/utm_source}} {{#utm_campaign}}
          <div class="field">
            <span class="label">UTM Campaign:</span>
            <span class="value">{{.}}</span>
          </div>
          {{/utm_campaign}} {{#utm_site}}
          <div class="field">
            <span class="label">UTM Site:</span>
            <span class="value">{{.}}</span>
          </div>
          {{/utm_site}} {{#utm_rep}}
          <div class="field">
            <span class="label">UTM Rep:</span>
            <span class="value">{{.}}</span>
          </div>
          {{/utm_rep}} {{#utm_af}}
          <div class="field">
            <span class="label">UTM Affiliate (utm_af):</span>
            <span class="value">{{.}}</span>
          </div>
          {{/utm_af}} {{#utm_referrer}}
          <div class="field">
            <span class="label">Referrer:</span>
            <span class="value">{{.}}</span>
          </div>
          {{/utm_referrer}} {{#utm_dur}}
          <div class="field">
            <span class="label">Form Duration:</span>
            <span class="value">{{.}} sec</span>
          </div>
          {{/utm_dur}}
        </div>
        {{/utm}}
      </div>
      <div class="footer">
        <p>This message was sent automatically on application completion.</p>
        <p>Application ID: {{uuid}}</p>
      </div>
    </div>
  </body>
</html>
