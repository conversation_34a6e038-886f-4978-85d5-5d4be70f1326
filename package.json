{"name": "app-sync-worker", "version": "6.0.0", "private": true, "scripts": {"deploy": "wrangler deploy", "deploy:dev": "wrangler deploy -e=dev", "dev": "wrangler dev -e=dev", "dev:remote": "wrangler dev -e=dev --x-remote-bindings", "bookmark:dev": "wrangler d1 time-travel info appsync-dev-db -e=dev", "bookmark:prod": "wrangler d1 time-travel info appsync-db", "migrate:local": "wrangler d1 migrations apply appsync-dev-db --local -e=dev", "migrate:dev": "wrangler d1 migrations apply appsync-dev-db --remote -e=dev", "migrate:prod": "read -p \"Type PROD to continue: \" input && [ \"$input\" = \"PROD\" ] && wrangler d1 migrations apply appsync-db --remote", "test": "vitest run"}, "devDependencies": {"@cloudflare/vitest-pool-workers": "^0.7.8", "vitest": "~3.0.9", "wrangler": "^4.24.3"}, "dependencies": {"@hono/zod-validator": "^0.4.3", "hono": "^4.8.4", "ua-parser-js": "^2.0.4", "zod": "^3.25.76", "pdf-lib": "^1.17.1"}}