{
  "$schema": "node_modules/wrangler/config-schema.json",
  "account_id": "aff33aa7ea15cd24e7d381a9f511ae60",
  "name": "appsync",
  "main": "src/index.js",
  "compatibility_date": "2025-06-17",
  "compatibility_flags": [
    "nodejs_compat"
  ],
  "workers_dev": false,
  "preview_urls": false,
  "observability": {
    "enabled": true
  },
  "d1_databases": [
    {
      "binding": "DB",
      "database_name": "appsync-db",
      "database_id": "d7d18be6-299a-40aa-b320-df44c7f1b9f7"
    }
  ],
  "unsafe": {
    "bindings": [
      {
        "name": "RATELIMIT",
        "type": "ratelimit",
        "namespace_id": "1000",
        // 12 requests per minute
        "simple": {
          "limit": 12,
          "period": 60
        }
      }
    ]
  },
  "routes": [
    {
      "pattern": "appsync.pinnaclefunding.com",
      "custom_domain": true
    }
  ],
  "kv_namespaces": [
    {
      "binding": "KV",
      "id": "8cc5168dd63a4e7e8e2b359aebb9715b",
      "preview_id": "995b542b3f0c41739f5043a424b7fdcc"
    },
    {
      "binding": "LOGS",
      "id": "ad9d3f1cfa804e2988fa56bad8a7e82b",
      "preview_id": "a377f4faa9d846b0829c9530ca3e5b4f"
    }
  ],
  "vars": {
    "VERSION": 6, // D1
    "EMAIL_QUEUE_DELAY": 1800, // 30 mins
    "PORTAL_URL": "https://app.pinnaclefunding.com",
    "SENDER_EMAIL": "Pinnacle Funding <<EMAIL>>",
    "ADMIN_EMAIL": "<EMAIL>",
    "ALLOWED_DOMAINS": [
      "app.pinnaclefunding.com",
      "ft.pinnaclefunding.com"
    ],
    "PREQUAL_TEMPLATE": "pre-qualify-approved",
    "DOC_SIGNED_TEMPLATE": "doc-signed",
    "ADMIN_PREQUAL_TEMPLATE": "admin-prequal",
    "ADMIN_FASTTRACK_TEMPLATE": "admin-fasttrack",
    "ADMIN_APP_COMPLETED_TEMPLATE": "admin-app-completed",
    "SAMPLE_AGENTS": [
      {
        "id": "005fL000003quxF",
        "name": "Avi Rapoport",
        "phone": "(*************",
        "email": "<EMAIL>",
        "image": "https://static.pinnaclefunding.com/agent-images/35.png",
        "calendlyUrl": "https://calendly.com/avi-pinnaclefundingco/30min"
      },
      {
        "id": "005fL000003qtxy",
        "name": "Israel Okunov",
        "phone": "(*************",
        "email": "<EMAIL>",
        "image": "https://static.pinnaclefunding.com/agent-images/20.png",
        "calendlyUrl": "https://calendly.com/israel-pinnaclefundingco/30min"
      },
      {
        "id": "005fL000003qvA9",
        "name": "Yakov Menoni",
        "phone": "(*************",
        "email": "<EMAIL>",
        "image": "https://static.pinnaclefunding.com/agent-images/37.png",
        "calendlyUrl": "https://calendly.com/yakov-pinnaclefundingco/30min"
      },
      {
        "id": "005fL000003qvJp",
        "name": "Yosef Halsband",
        "phone": "(*************",
        "email": "<EMAIL>",
        "image": "https://static.pinnaclefunding.com/agent-images/16.png",
        "calendlyUrl": "https://calendly.com/yosef-pinnaclefundingco/30min"
      },
      {
        "id": "005fL000003qv5J",
        "name": "Joel Axelrod",
        "phone": "(*************",
        "email": "<EMAIL>",
        "image": "https://static.pinnaclefunding.com/agent-images/48.png",
        "calendlyUrl": "https://calendly.com/joel-pinnaclefundingco/30min"
      },
      {
        "id": "005fL000003qv6v",
        "name": "Josh Fishman",
        "phone": "(*************",
        "email": "<EMAIL>",
        "image": "https://static.pinnaclefunding.com/agent-images/33.png",
        "calendlyUrl": "https://calendly.com/josh-pinnaclefundingco/30min"
      },
      {
        "id": "005fL000004IGlZ",
        "name": "Shlomo Ash",
        "phone": "(*************",
        "email": "<EMAIL>",
        "image": "https://static.pinnaclefunding.com/agent-images/42.png",
        "calendlyUrl": "https://calendly.com/shlomo-pinnaclefundingco/15min"
      }
    ],
    "TEST_AGENT": {
      "id": "005fL000003Q7lhQAC",
      "name": "Test Agent - Preassigned",
      "phone": "(*************",
      "email": "<EMAIL>",
      "image": "https://static.pinnaclefunding.com/agent-images/35.png",
      "calendlyUrl": "https://calendly.com/pinnaclefundingco-test"
    },
    "PANDADOC_API_BASE": "https://api.pandadoc.com/public/v1",
    "PANDADOC_TEMPLATE_UUID": "BmDKeds4zyERuEheQKTc2f",
    "PANDADOC_FOLDER_UUID": "ULVCU3LGAtoykKZo4dUrmP",
    "SALESFORCE_CONSUMER_KEY": "3MVG9cyb9OsYZthKSpr7sD2aWa1lM8GQSN5t7ep7hA_ibmV0CMF0GchkIjB8hP9iJaOpLBtFELfVcGiZygRN1",
    "SALESFORCE_USERNAME": "<EMAIL>",
    "SALESFORCE_API_VERSION": "v63.0"
  },
  "queues": {
    "producers": [
      {
        "queue": "appsync-email",
        "binding": "EMAIL_QUEUE"
      },
      {
        "queue": "appsync-salesforce",
        "binding": "SALESFORCE_QUEUE"
      },
      {
        "queue": "appsync-admin",
        "binding": "ADMIN_QUEUE"
      }
    ],
    "consumers": [
      {
        "queue": "appsync-email",
        "max_batch_size": 25,
        "max_batch_timeout": 2,
        "max_retries": 3,
        "retry_delay": 10
      },
      {
        "queue": "appsync-salesforce",
        "max_batch_size": 1,
        "max_batch_timeout": 0,
        "max_retries": 5,
        "retry_delay": 180
      },
      {
        "queue": "appsync-admin",
        "max_batch_size": 25,
        "max_batch_timeout": 2,
        "max_retries": 3,
        "retry_delay": 10
      }
    ]
  },
  "env": {
    "dev": {
      "routes": [
        {
          "pattern": "appsync-dev.pinnaclefunding.com",
          "custom_domain": true
        }
      ],
      "vars": {
        "DEV_MODE": true,
        "VERSION": 6, // D1
        "EMAIL_QUEUE_DELAY": 300,
        "PORTAL_URL": "https://dev-app.pinnaclefunding.com",
        "SENDER_EMAIL": "Pinnacle Funding Dev <<EMAIL>>",
        "ADMIN_EMAIL": "<EMAIL>",
        "ALLOWED_DOMAINS": [
          "localhost",
          "dev-app.pinnaclefunding.com",
          "app.pinnaclefunding.com",
          "ft.pinnaclefunding.com"
        ],
        "PREQUAL_TEMPLATE": "pre-qualify-approved",
        "DOC_SIGNED_TEMPLATE": "doc-signed",
        "ADMIN_PREQUAL_TEMPLATE": "admin-prequal",
        "ADMIN_FASTTRACK_TEMPLATE": "admin-fasttrack",
        "ADMIN_APP_COMPLETED_TEMPLATE": "admin-app-completed",
        "SAMPLE_AGENTS": [
          {
            "id": "005fL000003Q7lhQAC",
            "name": "Test Agent #1",
            "phone": "(*************",
            "email": "<EMAIL>",
            "image": "https://static.pinnaclefunding.com/agent-images/dev/35.png",
            "calendlyUrl": "https://calendly.com"
          },
          {
            "id": "005fL000003Q7lhQAC",
            "name": "Test Agent #2",
            "phone": "(*************",
            "email": "<EMAIL>",
            "image": "https://static.pinnaclefunding.com/agent-images/dev/20.png",
            "calendlyUrl": "https://calendly.com"
          },
          {
            "id": "005fL000003Q7lhQAC",
            "name": "Test Agent #3",
            "phone": "(*************",
            "email": "<EMAIL>",
            "image": "https://static.pinnaclefunding.com/agent-images/dev/generic.png",
            "calendlyUrl": "https://calendly.com"
          }
        ],
        "TEST_AGENT": {
          "id": "005fL000003Q7lhQAC",
          "name": "Test Agent - Preassigned",
          "phone": "(*************",
          "email": "<EMAIL>",
          "image": "https://static.pinnaclefunding.com/agent-images/dev/35.png",
          "calendlyUrl": "https://calendly.com/pinnaclefundingco-test"
        },
        "PANDADOC_API_BASE": "https://api.pandadoc.com/public/v1",
        "PANDADOC_TEMPLATE_UUID": "BmDKeds4zyERuEheQKTc2f",
        "PANDADOC_FOLDER_UUID": "yFEjH8V24TFsPpw5LyMaZH",
        "SALESFORCE_CONSUMER_KEY": "3MVG9cyb9OsYZthKSpr7sD2aWa1lM8GQSN5t7ep7hA_ibmV0CMF0GchkIjB8hP9iJaOpLBtFELfVcGiZygRN1",
        "SALESFORCE_USERNAME": "<EMAIL>",
        "SALESFORCE_API_VERSION": "v63.0"
      },
      "d1_databases": [
        {
          "binding": "DB",
          "database_name": "appsync-dev-db",
          "database_id": "7cf4b4cd-89a5-4e1a-a1fb-5029558d6af6"
        }
      ],
      "kv_namespaces": [
        {
          "binding": "KV",
          "id": "995b542b3f0c41739f5043a424b7fdcc", // Dev
          "experimental_remote": true
        },
        {
          "binding": "LOGS",
          "id": "a377f4faa9d846b0829c9530ca3e5b4f" // Dev
        }
      ],
      "unsafe": {
        "bindings": [
          {
            "name": "RATELIMIT",
            "type": "ratelimit",
            "namespace_id": "9000",
            // 12 requests per minute
            "simple": {
              "limit": 12,
              "period": 60
            }
          }
        ]
      },
      "queues": {
        "producers": [
          {
            "queue": "appsync-dev-email",
            "binding": "EMAIL_QUEUE"
          },
          {
            "queue": "appsync-dev-salesforce",
            "binding": "SALESFORCE_QUEUE"
          },
          {
            "queue": "appsync-dev-admin",
            "binding": "ADMIN_QUEUE"
          }
        ],
        "consumers": [
          {
            "queue": "appsync-dev-email",
            "max_batch_size": 25,
            "max_batch_timeout": 1,
            "max_retries": 2,
            "retry_delay": 10
          },
          {
            "queue": "appsync-dev-salesforce",
            "max_batch_size": 1,
            "max_batch_timeout": 0,
            "max_retries": 2,
            "retry_delay": 10
          },
          {
            "queue": "appsync-dev-admin",
            "max_batch_size": 25,
            "max_batch_timeout": 1,
            "max_retries": 2,
            "retry_delay": 10
          }
        ]
      }
    }
  }
}