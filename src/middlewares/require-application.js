import { ApplicationD1 } from '../db/applications';

export const requireApplication = async (c, next) => {
  const uuid = c.req.param('uuid');

  if (!uuid) {
    return c.json({ error: `Missing UUID` }, 400);
  }

  const application = await ApplicationD1.get(c.env, uuid);
  if (!application) {
    console.error(`Application "${uuid}" not found`);
    return c.json({ error: `Application "${uuid}" not found` }, 404);
  }

  c.set('application', application);
  console.log('App UUID:', uuid, application.status);
  await next();
};
