import { createFactory } from 'hono/factory';
import { cleanedApplication } from '../../utils/helpers';
import { requireApplication } from '../../middlewares/require-application';

const factory = createFactory();

export const getAppHandlers = factory.createHandlers(requireApplication, async (c) => {
  const application = c.get('application');
  return c.json({ data: cleanedApplication(application) });
});
