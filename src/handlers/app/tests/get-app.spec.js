import { SELF, env } from 'cloudflare:test';
import { describe, expect, it, beforeAll } from 'vitest';
import { ApplicationD1 } from '../../db/applications.js';

const BASE_URL = 'http://localhost:8787';

const genAppData = (uuid, status = 'PREQUAL_APPROVED', overrides = {}) => {
  const timestamp = new Date().toISOString();

  return {
    uuid,
    version: 6,
    status,
    domain: 'app.pinnaclefunding.com',
    preQualifyFields: {
      fundingAmount: 75000,
      purpose: 'Expansion',
      businessName: 'Test Business LLC',
      monthlyRevenue: '************',
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      phone: '1234567890',
      estimatedFICO: '700-780',
      consent: true,
    },
    applicationFields: {
      businessName: 'Test Business LLC',
      entityType: 'LLC',
      ein: '123456789',
      address: {
        line1: '123 Test St',
        city: 'Test City',
        state: 'NY',
        zip: '12345',
      },
      owners: [
        {
          firstName: '<PERSON>',
          lastName: 'Doe',
          ssn: '123456789',
          dateOfBirth: '1980-01-01',
          ownershipPercentage: 100,
        },
      ],
    },
    agent: {
      id: 'agent-123',
      name: 'Test Agent',
      email: '<EMAIL>',
    },
    pandadoc: {
      documentId: 'doc-123',
      status: 'draft',
    },
    meta: {
      initiated: {
        timestamp: timestamp,
        userAgent: 'test-agent',
      },
    },
    utm: {
      utm_source: 'google',
      utm_medium: 'cpc',
    },
    fastTrack: false,
    approvalAmount: 75000,
    reason: null,
    salesforce_id: null,
    bank_stmts: 0,
    created_at: timestamp,
    updated_at: timestamp,
    started_at: null,
    submitted_at: null,
    signed_at: null,
    completed_at: null,
    edited_at: null,
    ...overrides,
  };
};

describe('Get App Handler', () => {
  let testApps = {};

  beforeAll(async () => {
    const approvedApp = genAppData('test-approved-uuid', 'PREQUAL_APPROVED');
    const deniedApp = genAppData('test-denied-uuid', 'PREQUAL_DENIED', {
      reason: 'start_date',
      approvalAmount: null,
      agent: null,
    });
    const startedApp = genAppData('test-started-uuid', 'APP_STARTED', {
      started_at: new Date().toISOString(),
    });
    const submittedApp = genAppData('test-submitted-uuid', 'APP_SUBMITTED', {
      submitted_at: new Date().toISOString(),
    });
    const completedApp = genAppData('test-completed-uuid', 'APP_COMPLETED', {
      completed_at: new Date().toISOString(),
    });

    await Promise.all([
      ApplicationD1.create(env, approvedApp),
      ApplicationD1.create(env, deniedApp),
      ApplicationD1.create(env, startedApp),
      ApplicationD1.create(env, submittedApp),
      ApplicationD1.create(env, completedApp),
    ]);

    testApps = {
      approved: approvedApp,
      denied: deniedApp,
      started: startedApp,
      submitted: submittedApp,
      completed: completedApp,
    };
  });
