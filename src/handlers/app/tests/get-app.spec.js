import { SELF, env } from 'cloudflare:test';
import { describe, expect, it, beforeAll } from 'vitest';
import { ApplicationD1 } from '../../db/applications.js';

const BASE_URL = 'http://localhost:8787';

const genAppData = (uuid, status = 'PREQUAL_APPROVED', overrides = {}) => {
  const timestamp = new Date().toISOString();

  return {
    uuid,
    version: 6,
    status,
    domain: 'app.pinnaclefunding.com',
    preQualifyFields: {
      fundingAmount: 75000,
      purpose: 'Expansion',
      businessName: 'Test Business LLC',
      monthlyRevenue: '************',
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      phone: '1234567890',
      estimatedFICO: '700-780',
      consent: true,
    },
    applicationFields: {
      businessName: 'Test Business LLC',
      entityType: 'LLC',
      ein: '123456789',
      address: {
        line1: '123 Test St',
        city: 'Test City',
        state: 'NY',
        zip: '12345',
      },
      owners: [
        {
          firstName: '<PERSON>',
          lastName: 'Doe',
          ssn: '123456789',
          dateOfBirth: '1980-01-01',
          ownershipPercentage: 100,
        },
      ],
    },
    agent: {
      id: 'agent-123',
      name: 'Test Agent',
      email: '<EMAIL>',
    },
    pandadoc: {
      documentId: 'doc-123',
      status: 'draft',
    },
    meta: {
      initiated: {
        timestamp: timestamp,
        userAgent: 'test-agent',
      },
    },
    utm: {
      utm_source: 'google',
      utm_medium: 'cpc',
    },
    fastTrack: false,
    approvalAmount: 75000,
    reason: null,
    salesforce_id: null,
    bank_stmts: 0,
    created_at: timestamp,
    updated_at: timestamp,
    started_at: null,
    submitted_at: null,
    signed_at: null,
    completed_at: null,
    edited_at: null,
    ...overrides,
  };
};

describe('Get App Handler', () => {
  let testApps = {};

  beforeAll(async () => {
    const approvedApp = genAppData('test-approved-uuid', 'PREQUAL_APPROVED');
    const deniedApp = genAppData('test-denied-uuid', 'PREQUAL_DENIED', {
      reason: 'start_date',
      approvalAmount: null,
      agent: null,
    });
    const startedApp = genAppData('test-started-uuid', 'APP_STARTED', {
      started_at: new Date().toISOString(),
    });
    const submittedApp = genAppData('test-submitted-uuid', 'APP_SUBMITTED', {
      submitted_at: new Date().toISOString(),
    });
    const completedApp = genAppData('test-completed-uuid', 'APP_COMPLETED', {
      completed_at: new Date().toISOString(),
    });

    await Promise.all([
      ApplicationD1.create(env, approvedApp),
      ApplicationD1.create(env, deniedApp),
      ApplicationD1.create(env, startedApp),
      ApplicationD1.create(env, submittedApp),
      ApplicationD1.create(env, completedApp),
    ]);

    testApps = {
      approved: approvedApp,
      denied: deniedApp,
      started: startedApp,
      submitted: submittedApp,
      completed: completedApp,
    };
  });

  it('retrieves applications with correct data based on status and cleans PII', async () => {
    // Test PREQUAL_APPROVED - should include preQualifyFields
    let response = await SELF.fetch(`${BASE_URL}/app/${testApps.approved.uuid}`);
    expect(response.status).toBe(200);

    let data = await response.json();
    expect(data.data).toMatchObject({
      uuid: testApps.approved.uuid,
      status: 'PREQUAL_APPROVED',
      version: 6,
      fastTrack: false,
      approvalAmount: 75000,
      preQualifyFields: testApps.approved.preQualifyFields,
      agent: {
        name: 'Test Agent',
        email: '<EMAIL>',
      },
    });
    expect(data.data.agent.id).toBeUndefined();
    expect(data.data.meta).toBeUndefined();
    expect(data.data.utm).toBeUndefined();

    // Test PREQUAL_DENIED - should include reason and limited fields
    response = await SELF.fetch(`${BASE_URL}/app/${testApps.denied.uuid}`);
    expect(response.status).toBe(200);

    data = await response.json();
    expect(data.data).toMatchObject({
      uuid: testApps.denied.uuid,
      status: 'PREQUAL_DENIED',
      reason: 'start_date',
      preQualifyFields: {
        firstName: 'John',
        businessName: 'Test Business LLC',
      },
    });
    expect(data.data.preQualifyFields.email).toBeUndefined();
    expect(data.data.approvalAmount).toBeUndefined();

    // Test APP_STARTED - should include preQualifyFields
    response = await SELF.fetch(`${BASE_URL}/app/${testApps.started.uuid}`);
    expect(response.status).toBe(200);

    data = await response.json();
    expect(data.data).toMatchObject({
      uuid: testApps.started.uuid,
      status: 'APP_STARTED',
      preQualifyFields: testApps.started.preQualifyFields,
    });

    // Test APP_SUBMITTED - should include pandadoc and limited fields
    response = await SELF.fetch(`${BASE_URL}/app/${testApps.submitted.uuid}`);
    expect(response.status).toBe(200);

    data = await response.json();
    expect(data.data).toMatchObject({
      uuid: testApps.submitted.uuid,
      status: 'APP_SUBMITTED',
      pandadoc: testApps.submitted.pandadoc,
      preQualifyFields: {
        firstName: 'John',
        businessName: 'Test Business LLC',
      },
    });
    expect(data.data.preQualifyFields.email).toBeUndefined();

    // Test APP_COMPLETED - should include limited fields
    response = await SELF.fetch(`${BASE_URL}/app/${testApps.completed.uuid}`);
    expect(response.status).toBe(200);

    data = await response.json();
    expect(data.data).toMatchObject({
      uuid: testApps.completed.uuid,
      status: 'APP_COMPLETED',
      preQualifyFields: {
        fundingAmount: 75000,
      },
      applicationFields: {
        businessName: 'Test Business LLC',
        owners: [
          {
            firstName: 'John',
          },
        ],
      },
    });
    expect(data.data.applicationFields.ein).toBeUndefined();
    expect(data.data.applicationFields.owners[0].ssn).toBeUndefined();
    expect(data.data.applicationFields.owners[0].dateOfBirth).toBeUndefined();
  });

  it('returns 404 for non-existing application', async () => {
    const response = await SELF.fetch(`${BASE_URL}/app/non-existing-uuid`);

    expect(response.status).toBe(404);

    const data = await response.json();
    expect(data.error).toBe('Application "non-existing-uuid" not found');
  });

  it('returns 400 for missing UUID parameter', async () => {
    const response = await SELF.fetch(`${BASE_URL}/app/`);

    expect(response.status).toBe(404);
  });
});
