import { createFactory } from 'hono/factory';
import { ApplicationD1 } from '../../db/applications';
import { SendToQueue } from '../../queues';
import { getMeta } from '../../utils';
import { AppError, cleanedApplication } from '../../utils/helpers';
import { requireApplication } from '../../middlewares/require-application';

const factory = createFactory();

export const startAppHandlers = factory.createHandlers(requireApplication, async (c) => {
  const timestamp = c.get('timestamp');
  let application = c.get('application');

  if (!['PREQUAL_APPROVED', 'PREQUAL_FAST_TRACK'].includes(application.status)) {
    console.error(`Application status ${application.status} can't be started`);
    throw new AppError(`Application can't be started`, 400, 'startApp', `Status is ${application.status}`);
  }

  const columnsToUpdate = {
    started_at: timestamp,
    status: 'APP_STARTED',
    meta: { ...application.meta, started: getMeta(c.req.raw, timestamp) },
  };

  application = { ...application, ...columnsToUpdate };

  await ApplicationD1.update(c.env, application.uuid, columnsToUpdate);
  c.executionCtx.waitUntil(SendToQueue.salseforce(c.env, { application }, { delaySeconds: 5 }));

  return c.json({ data: cleanedApplication(application) });
});
