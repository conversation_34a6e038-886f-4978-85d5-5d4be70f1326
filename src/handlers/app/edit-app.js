import { createFactory } from 'hono/factory';
import { ApplicationD1 } from '../../db/applications';
import { moveDocumentToDraft } from '../../pandadoc';
import { SendToQueue } from '../../queues';
import { getMeta } from '../../utils';
import { AppError, cleanedApplication } from '../../utils/helpers';
import { requireApplication } from '../../middlewares/require-application';

const factory = createFactory();

export const editAppHandlers = factory.createHandlers(requireApplication, async (c) => {
  const timestamp = c.get('timestamp');
  let application = c.get('application');

  if (application.status === 'APP_SUBMITTED') {
    const document = await moveDocumentToDraft(c.env, application.pandadoc?.document?.id);

    const columnsToUpdate = {
      pandadoc: { document },
      status: 'APP_EDITING',
      edited_at: timestamp,
      meta: { ...application.meta, edited: getMeta(c.req.raw, timestamp) },
    };

    application = { ...application, ...columnsToUpdate };

    await ApplicationD1.update(c.env, application.uuid, columnsToUpdate);
    c.executionCtx.waitUntil(SendToQueue.salseforce(c.env, { application }, { delaySeconds: 2 }));

    return c.json({ data: cleanedApplication(application) });
  } else {
    console.error(`Application status ${application.status} can't be edited`);
    throw new AppError(`Application can't be edited`, 400, 'editApp', `Status is ${application.status}`);
  }
});
