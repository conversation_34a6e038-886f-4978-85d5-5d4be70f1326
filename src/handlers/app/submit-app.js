import { zValidator } from '@hono/zod-validator';
import { createFactory } from 'hono/factory';
import { z } from 'zod';
import { ApplicationD1 } from '../../db/applications';
import { putAppPII } from '../../kv';
import { createEmbeddedSession, createPandaDocSession, sendPandaDocSilently, updatePandaDocFields } from '../../pandadoc';
import { SendToQueue } from '../../queues';
import { applicationSchema } from '../../schema/application';
import { getMeta } from '../../utils';
import { AppError, cleanedApplication, extractAndSanitizePIIFields } from '../../utils/helpers';
import { requireApplication } from '../../middlewares/require-application';

const factory = createFactory();

const validator = zValidator('json', z.object({ applicationFields: applicationSchema }), async (result) => {
  if (!result.success) {
    console.log(JSON.stringify(result.error.issues, null, 2));
    throw new AppError('Validation Error: Invalid applicationFields', 400, 'validationError', result.error);
  }
});

export const submitAppHandlers = factory.createHandlers(validator, requireApplication, async (c) => {
  const timestamp = c.get('timestamp');
  const uuid = c.req.param('uuid');

  const fetchApplication = c.get('application');
  const application = { ...fetchApplication };
  let applicationFields = c.req.valid('json').applicationFields;

  const { piiData, sanitizedApplicationFields } = extractAndSanitizePIIFields(applicationFields);

  if (!application.meta) {
    application.meta = {};
  }
  application.meta.submitted = getMeta(c.req.raw, timestamp);
  application.submitted_at = timestamp;

  if (application.status === 'APP_EDITING') {
    application.applicationFields = {
      ...(application.applicationFields || {}),
      ...applicationFields,
    };

    await updatePandaDocFields(c.env, application);
    await sendPandaDocSilently(c.env, application.pandadoc.document.id);
    const session = await createEmbeddedSession(c.env, application.pandadoc.document.id, application);
    application.pandadoc.session = session;
  } else {
    application.applicationFields = applicationFields;
    if (!application.pandadoc) {
      const pandadoc = await createPandaDocSession(c.env, application);
      application.pandadoc = pandadoc;
    }
  }

  application.status = 'APP_SUBMITTED';
  application.applicationFields = sanitizedApplicationFields;

  const columnsToUpdate = {
    applicationFields: sanitizedApplicationFields,
    meta: application.meta,
    status: application.status,
    submitted_at: application.submitted_at,
    pandadoc: application.pandadoc,
  };

  await Promise.all([
    putAppPII(c.env, uuid, piiData),
    ApplicationD1.update(c.env, application.uuid, columnsToUpdate),
    SendToQueue.salseforce(c.env, { application }),
  ]);

  return c.json({ data: cleanedApplication(application) });
});
