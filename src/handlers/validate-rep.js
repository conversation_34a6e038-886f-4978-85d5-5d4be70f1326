import { createFactory } from 'hono/factory';
import { Agents } from '../classes/agents';
import { AppError } from '../utils/helpers';

const factory = createFactory();

export const validateRepHandlers = factory.createHandlers(async (c) => {
  const rep = c.req.param('rep')?.trim().toLowerCase();

  if (!rep) {
    throw new AppError('Missing rep parameter', 400, 'validationError');
  }

  const isValidRep = Boolean(await Agents.findAgent(c.env, rep));

  console.log(`'${rep}'`, 'is valid:', isValidRep);

  return c.json({
    rep,
    valid: isValidRep,
  });
});
