import { Hono } from 'hono';

// Middlewares
import { attachTimestampMiddleware } from './middlewares/attach-timestamp';
import { apiBodyLimitMiddleware } from './middlewares/body-limit';
import { corsMiddleware } from './middlewares/cors';
import { rateLimitMiddleware } from './middlewares/rate-limit';

// Handlers
import { completeAppHandlers } from './handlers/app/complete-app';
import { createtAppHandlers } from './handlers/app/create-app';
import { createAppFasttrackHandlers } from './handlers/app/create-app-fasttrack';
import { editAppHandlers } from './handlers/app/edit-app';
import { getAppHandlers } from './handlers/app/get-app';
import { pandadocStatusHandlers } from './handlers/app/pandadoc-status';
import { signAppHandlers } from './handlers/app/sign-app';
import { startAppHandlers } from './handlers/app/start-app';
import { submitAppHandlers } from './handlers/app/submit-app';
import { errorHandler } from './handlers/error/error-handler';
import { exportDataHandlers } from './handlers/export-data';
import { validateRepHandlers } from './handlers/validate-rep';

// Queue Router
import { QueueRouter } from './queues';

const app = new Hono();

app.use('*', corsMiddleware);
app.use('*', rateLimitMiddleware);
app.use('*', apiBodyLimitMiddleware);
app.use('*', attachTimestampMiddleware);

app.get('/', (c) => c.text('OK'));

app.post('/app', ...createtAppHandlers);
app.post('/app/fasttrack', ...createAppFasttrackHandlers);
app.get('/app/:uuid', ...getAppHandlers);

app.post('/app/:uuid/submit', ...submitAppHandlers);
app.post('/app/:uuid/edit', ...editAppHandlers);
app.post('/app/:uuid/start', ...startAppHandlers);
app.post('/app/:uuid/sign', ...signAppHandlers);
app.post('/app/:uuid/complete', ...completeAppHandlers);

app.get('/app/:uuid/pandadoc/status', ...pandadocStatusHandlers);

app.get('/reps/:rep/validate', ...validateRepHandlers);

app.get('/export', ...exportDataHandlers);

app.notFound((c) => c.text('Not Found', 404));

app.onError(errorHandler);

export default {
  // Handle HTTP Requests
  fetch: app.fetch,

  // Handle Queue Messages (consumer)
  async queue(batch, env) {
    return QueueRouter(batch, env);
  },
};

// RPC Classes + Methods
import { WorkerEntrypoint } from 'cloudflare:workers';

import { RoundRobin } from './classes/round-robin';
import { Agents } from './classes/agents';

export class AdminTools extends WorkerEntrypoint {
  async ping() {
    return 'pong';
  }

  async RoundRobin(method, ...args) {
    console.log(`RoundRobin.${method}`, ...args);
    if (typeof RoundRobin[method] !== 'function') throw Error(`Method ${method} not found`);
    return RoundRobin[method](this.env, ...args);
  }

  async Agents(method, ...args) {
    console.log(`Agents.${method}`, ...args);
    if (typeof Agents[method] !== 'function') throw Error(`Method ${method} not found`);
    return Agents[method](this.env, ...args);
  }
}
