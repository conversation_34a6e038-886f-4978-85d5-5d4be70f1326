export class MetadataUtils {
  static getEmojiFlag(meta) {
    const { country } = meta;
    if (!/^[a-zA-Z]{2}$/.test(country)) {
      return '❓'; // Default fallback emoji for invalid input
    }
    const codePoints = country
      ?.toUpperCase()
      .split('')
      .map((char) => 127397 + char.charCodeAt(0));
    return String.fromCodePoint(...codePoints);
  }

  static formatIpLocation(meta) {
    const { country, city, region } = meta;
    const regionNames = new Intl.DisplayNames(['en'], { type: 'region' });
    const countryName = regionNames.of(country?.toUpperCase());

    if (country === 'US') {
      return `${city}, ${region}`;
    } else {
      return `${city || region}, ${countryName}`;
    }
  }
}
