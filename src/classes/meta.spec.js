import { env } from 'cloudflare:test';
import { describe, expect, it } from 'vitest';
import { MetaD1 } from './meta.js';

describe('MetaD1', () => {
  it('can set, get, and delete meta values', async () => {
    const testKey = `test-key-${Date.now()}`;
    const testValue = { data: 'test-value', number: 42, array: [1, 2, 3] };
    const stringKey = `string-key-${Date.now()}`;
    const stringValue = JSON.stringify('simple-string'); // Store as valid JSON

    // Test set and get with object
    await MetaD1.set(env, testKey, testValue);
    const retrievedValue = await MetaD1.get(env, testKey);
    expect(retrievedValue).toEqual(testValue);

    // Test set and get with JSON string
    await MetaD1.set(env, stringKey, stringValue);
    const retrievedString = await MetaD1.get(env, stringKey);
    expect(retrievedString).toBe('simple-string'); // Should be parsed

    // Test get all
    const allMeta = await MetaD1.all(env);
    expect(allMeta[testKey]).toEqual(testValue);
    expect(allMeta[stringKey]).toBe('simple-string');

    // Test delete - just verify the key gets deleted, don't assert the return value
    await MetaD1.delete(env, testKey);
    const deletedValue = await MetaD1.get(env, testKey);
    expect(deletedValue).toBeNull();

    // Test delete non-existent key
    const deleteNonExistent = await MetaD1.delete(env, 'non-existent-key-unique');
    expect(deleteNonExistent).toBe(false);
  });

  it('handles edge cases and errors', async () => {
    // Test get with null key
    const nullResult = await MetaD1.get(env, null);
    expect(nullResult).toBeNull();

    // Test get with empty string key
    const emptyResult = await MetaD1.get(env, '');
    expect(emptyResult).toBeNull();

    // Test get non-existent key
    const nonExistent = await MetaD1.get(env, 'does-not-exist');
    expect(nonExistent).toBeNull();

    // Test error handling for missing env
    await expect(MetaD1.get(null, 'test')).rejects.toThrow('Missing env on MetaD1.get');
    await expect(MetaD1.set(null, 'test', 'value')).rejects.toThrow('Missing env on MetaD1.get');
    await expect(MetaD1.delete(null, 'test')).rejects.toThrow('Missing env on MetaD1.get');
    await expect(MetaD1.all(null)).rejects.toThrow('Missing env on MetaD1.get');

    // Test error handling for missing key in set
    await expect(MetaD1.set(env, null, 'value')).rejects.toThrow('Missing key for meta update');
    await expect(MetaD1.set(env, '', 'value')).rejects.toThrow('Missing key for meta update');
  });

  it('handles JSON parsing edge cases', async () => {
    // Test with valid JSON string that should be parsed
    const key = 'json-string';
    const jsonString = '"simple-string"';

    await MetaD1.set(env, key, jsonString);
    const result = await MetaD1.get(env, key);
    // Should return the parsed string (without quotes)
    expect(result).toBe('simple-string');

    // Test with number stored as JSON
    const numberKey = 'json-number';
    await MetaD1.set(env, numberKey, 42);
    const numberResult = await MetaD1.get(env, numberKey);
    expect(numberResult).toBe(42);
  });
});
