import { env } from 'cloudflare:test';
import { describe, expect, it, beforeEach } from 'vitest';
import { Agents } from './agents.js';
import { MetaD1 } from './meta.js';

const createTestAgents = () => [
  {
    id: 'agent-1',
    name: '<PERSON>',
    email: '<EMAIL>',
    roundRobin: true,
  },
  {
    id: 'agent-2',
    name: '<PERSON>',
    email: '<EMAIL>',
    roundRobin: false,
  },
  {
    id: 'agent-3',
    name: '<PERSON>',
    email: '<EMAIL>',
    roundRobin: true,
  },
];

describe('Agents', () => {
  beforeEach(async () => {
    // Clean up agents before each test
    await MetaD1.delete(env, 'agents:list');
  });

  it('can set, get, and update agents', async () => {
    const testAgents = createTestAgents();

    // Test setAgents and getAgents
    await Agents.setAgents(env, testAgents);
    const retrievedAgents = await Agents.getAgents(env);
    expect(retrievedAgents).toEqual(testAgents);

    // Test updateAgent
    const updatedFields = { name: '<PERSON>', roundRobin: false };
    const updateResult = await Agents.updateAgent(env, 'agent-1', updatedFields);

    expect(updateResult.agent).toEqual({
      id: 'agent-1',
      name: 'John Updated',
      email: '<EMAIL>',
      roundRobin: false,
    });
    expect(updateResult.index).toBe(0);

    // Verify the update persisted
    const updatedAgents = await Agents.getAgents(env);
    expect(updatedAgents[0].name).toBe('John Updated');
    expect(updatedAgents[0].roundRobin).toBe(false);
  });

  it('handles empty agents list', async () => {
    const emptyAgents = await Agents.getAgents(env);
    expect(emptyAgents).toEqual([]);
  });

  it('can find agents by username', async () => {
    const testAgents = createTestAgents();
    await Agents.setAgents(env, testAgents);

    // Test finding existing agent
    const foundAgent = await Agents.findAgent(env, 'john.doe');
    expect(foundAgent).toEqual({
      id: 'agent-1',
      name: 'John Doe',
      email: '<EMAIL>',
      assigned_at: expect.any(String),
    });

    // Test case insensitive search
    const foundAgentCaseInsensitive = await Agents.findAgent(env, 'JANE.SMITH');
    expect(foundAgentCaseInsensitive).toEqual({
      id: 'agent-2',
      name: 'Jane Smith',
      email: '<EMAIL>',
      assigned_at: expect.any(String),
    });

    // Test agent not found
    const notFound = await Agents.findAgent(env, 'nonexistent');
    expect(notFound).toBeNull();

    // Test test agent
    const testAgent = await Agents.findAgent(env, 'test');
    expect(testAgent).toBe(env.TEST_AGENT);
  });

  it('formats agent correctly', () => {
    const agent = {
      id: 'agent-1',
      name: 'John Doe',
      email: '<EMAIL>',
      roundRobin: true,
    };

    const formatted = Agents.formatAgent(agent);
    expect(formatted).toEqual({
      id: 'agent-1',
      name: 'John Doe',
      email: '<EMAIL>',
      assigned_at: expect.any(String),
    });
    expect(formatted.roundRobin).toBeUndefined();
  });

  it('handles errors correctly', async () => {
    // Test setAgents with non-array
    await expect(Agents.setAgents(env, 'not-an-array')).rejects.toThrow('Agents list must be an array');
    await expect(Agents.setAgents(env, null)).rejects.toThrow('Agents list must be an array');

    // Test updateAgent with non-existent agent
    await Agents.setAgents(env, createTestAgents());
    await expect(Agents.updateAgent(env, 'non-existent', { name: 'Updated' })).rejects.toThrow('Agent not found');
  });
});
