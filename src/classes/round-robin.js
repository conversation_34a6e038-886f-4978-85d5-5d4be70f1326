import { MetaD1 } from './meta';
import { Agents } from './agents';

const INDEX_KEY = 'round-robin:index';

export class RoundRobin {
  static async getAgents(env) {
    const agentsList = await Agents.getAgents(env);
    return agentsList.filter((a) => a.roundRobin);
  }

  // Control index (Up Next)
  static async getIndex(env) {
    const index = await MetaD1.get(env, INDEX_KEY);
    return parseInt(index ?? '') || 0;
  }

  static async setIndex(env, index) {
    if (typeof index !== 'number' || index < 0) {
      throw new Error('Index must be a non-negative integer');
    }

    // enforce integer (floor floats)
    const intIndex = Math.floor(index);

    await MetaD1.set(env, INDEX_KEY, intIndex.toString());
    return intIndex;
  }

  static async next(env) {
    const agentsList = await this.getAgents(env);
    if (!agentsList?.length) {
      throw new Error('No agents available for round-robin assignment');
    }

    // must avoid race condition here
    const n = agentsList.length;
    const result = await env.DB.prepare(
      `UPDATE meta SET value = CAST(CAST((CAST(value AS INTEGER) + 1) % ? AS INTEGER) AS TEXT)
       WHERE key = ? RETURNING
         CASE
         WHEN CAST(value AS INTEGER) = 0 THEN
         ? - 1
         ELSE CAST(value AS INTEGER) - 1
         END AS idx`
    )
      .bind(n, INDEX_KEY, n)
      .first();
    const idx = result?.idx ?? 0;

    console.log('RoundRobin.next', 'agents:', agentsList.map((o) => o.name).join(', '));
    console.log('RoundRobin.next', 'index:', idx);

    const agent = {
      ...agentsList[idx],
      assigned_at: new Date().toISOString(),
    };

    return agent;
  }

  static async previous(env) {
    const agentsList = await this.getAgents(env);
    if (!agentsList?.length) {
      throw new Error('No agents available for round-robin assignment');
    }

    const n = agentsList.length;
    const result = await env.DB.prepare(
      `UPDATE meta SET value = CAST(CAST((CAST(value AS INTEGER) - 1 + ?) % ? AS INTEGER) AS TEXT)
       WHERE key = ? RETURNING
         CASE
         WHEN CAST(value AS INTEGER) = ? - 1 THEN
         0
         ELSE CAST(value AS INTEGER) + 1
         END AS idx`
    )
      .bind(n, n, INDEX_KEY, n)
      .first();
    const idx = result?.idx ?? 0;

    console.log('RoundRobin.previous', 'agents:', agentsList.map((o) => o.name).join(', '));
    console.log('RoundRobin.previous', 'index:', idx);

    const agent = {
      ...agentsList[idx],
      assigned_at: new Date().toISOString(),
    };

    return agent;
  }

  // enable/disable agents
  static async enableAgent(env, agentId) {
    const { agent } = await Agents.updateAgent(env, agentId, { roundRobin: true });
    const currentIndex = await this.getIndex(env);
    // get round-robin agents after disabling - so it can determine the position
    const rrAgents = await this.getAgents(env);
    const index = rrAgents.findIndex((a) => a.id === agentId);

    if (index !== -1 && index <= currentIndex) {
      // if the new agent was inserted before or at the current index → next() to shift the pointer forward
      await this.next(env);
    }

    return agent;
  }

  static async disableAgent(env, agentId) {
    // get round-robin agents before disabling - so it can determine if it's allowed + the position
    const rrAgents = await this.getAgents(env);
    if (rrAgents.length === 1) {
      throw new Error('Cannot disable the only remaining agent. There must be at least one agent in round-robin.');
    }
    const index = rrAgents.findIndex((a) => a.id === agentId);

    const currentIndex = await this.getIndex(env);

    const { agent } = await Agents.updateAgent(env, agentId, { roundRobin: false });

    if (index !== -1 && index < currentIndex) {
      // if the removed agent was before the current index → previous() to shift the pointer back
      await this.previous(env);
    }

    return agent;
  }
}
