import { env } from 'cloudflare:test';
import { describe, expect, it } from 'vitest';
import { ApplicationD1 } from './applications.js';

const genAppData = (uuid, fastTrack = false, sfId = null, overrides = {}) => {
  const testUuid = uuid || 'test-uuid-123';
  const timestamp = new Date().toISOString();

  return {
    uuid: testUuid,
    version: 6,
    status: 'PREQUAL_APPROVED',
    domain: 'app.pinnaclefunding.com',
    preQualifyFields: {
      fundingAmount: 75000,
      purpose: 'Expansion',
      businessName: 'Test Business LLC',
      monthlyRevenue: '************',
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      phone: '1234567890',
      estimatedFICO: '700-780',
      consent: true,
    },
    applicationFields: {
      businessName: 'Test Business LLC',
      entityType: 'LLC',
      ein: '123456789',
      address: {
        line1: '123 Test St',
        city: 'Test City',
        state: 'NY',
        zip: '12345',
      },
      owners: [
        {
          firstName: 'John',
          lastName: 'Doe',
          ownershipPercentage: 100,
        },
      ],
    },
    agent: {
      id: 'agent-123',
      name: 'Test Agent',
      email: '<EMAIL>',
    },
    pandadoc: {
      documentId: 'doc-123',
      status: 'draft',
    },
    utm: {
      utm_source: 'google',
      utm_medium: 'cpc',
      utm_campaign: 'test-campaign',
    },
    meta: {
      initiated: {
        timestamp: timestamp,
        userAgent: 'test-agent',
      },
    },
    fastTrack: fastTrack,
    approvalAmount: 50000,
    reason: null,
    salesforce_id: sfId,
    bank_stmts: 3,
    created_at: timestamp,
    updated_at: timestamp,
    started_at: timestamp,
    submitted_at: null,
    signed_at: null,
    completed_at: null,
    edited_at: null,
    ...overrides,
  };
};

describe('ApplicationD1', () => {
  it('can create, get, update, and getAll applications', async () => {
    const appData = genAppData('test-1', false);
    const appData2 = genAppData('test-2', true, 'A'.repeat(18), {
      extraField: 'should-not-be-stored',
    });

    await Promise.all([ApplicationD1.create(env, appData), ApplicationD1.create(env, appData2)]);

    const app1 = await ApplicationD1.get(env, appData.uuid);
    const app2 = await ApplicationD1.get(env, appData2.uuid);

    expect(app1).toBeDefined();
    expect(app2).toBeDefined();

    expect(app1.fastTrack).toBe(false);
    expect(app2.fastTrack).toBe(true);

    expect(app1.salesforce_id).toBeNull();
    expect(app2.salesforce_id).toBe('A'.repeat(18));

    expect(app1.extraField).toBeUndefined();
    expect(app2.extraField).toBeUndefined();

    expect(app1.preQualifyFields).toEqual(appData.preQualifyFields);
    expect(app1.applicationFields).toEqual(appData.applicationFields);

    await ApplicationD1.update(env, appData.uuid, {
      status: 'APP_STARTED',
      started_at: new Date().toISOString(),
      salesforce_id: 'B'.repeat(18),
    });

    const updatedApp = await ApplicationD1.get(env, appData.uuid);
    expect(updatedApp.status).toBe('APP_STARTED');
    expect(updatedApp.started_at).toBeDefined();
    expect(updatedApp.salesforce_id).toBe('B'.repeat(18));

    const allApps = await ApplicationD1.getAll(env);
    expect(allApps).toHaveLength(2);
  });

  it('throws error D1_ERROR with invalid data', async () => {
    const appData = genAppData('test-3', false, null, {
      preQualifyFields: null,
      status: null,
    });

    await expect(ApplicationD1.create(env, appData)).rejects.toThrowErrorMatchingInlineSnapshot(
      `[Error: D1_ERROR: NOT NULL constraint failed: applications.status: SQLITE_CONSTRAINT]`
    );
  });

  it('should throw error when creating without uuid', async () => {
    await expect(ApplicationD1.create(env, { status: 'PREQUAL_APPROVED' })).rejects.toThrow('Missing UUID for D1 create');
  });

  it('should throw error when creating with null data', async () => {
    await expect(ApplicationD1.create(env, null)).rejects.toThrow('Missing UUID for D1 create');
  });
});
